# Configurações globais do CodeRabbit para projetos em Angular
language: "pt-BR"
tone_instructions: "Seja direto e foque apenas em problemas críticos que possam causar bugs ou falhas de segurança. Evite comentários de estilo ou sugestões de refatoração."

reviews:
  # Perfil chill para revisões menos rigorosas
  profile: "chill"

  # Configurações para reduzir conteúdo desnecessário
  high_level_summary: false
  changed_files_summary: false
  sequence_diagrams: false
  poem: false
  suggested_labels: false

  # Branches para revisão
  base_branches:
    - master
    - main
    - development
    - develop

  # Filtros de caminho para ignorar arquivos gerados
  path_filters:
    - "!.coderabbit.yaml"
    - "!**/*.ngfactory.ts"
    - "!**/*.generated.ts"
    - "!**/*.min.js"

  # Instruções específicas para TypeScript/JavaScript
  path_instructions:
    - path: "**/*.{ts,js}"
      instructions: |
        - Foque APENAS em bugs, problemas de segurança (exceto secrets) ou incompatibilidades
        - NÃO comente sobre estilo, formatação ou refatorações
        - NÃO sugira melhorias de código que não afetem funcionalidade
        - Consulte package.json antes de sugerir mudanças de dependências
        - Ignore arquivos gerados automaticamente pelo Angular
        - NÃO sugira mudanças de var para let/const por estilo

  # Configurações de ferramentas - apenas críticas
  tools:
    eslint:
      enabled: true
    # Desabilitar linters de estilo
    checkstyle:
      enabled: false
    spotbugs:
      enabled: false

# Configurações de base de conhecimento
knowledge_base:
  code_guidelines:
    enabled: true
    filePatterns:
      - "**/.eslintrc*"
      - "**/tsconfig.json"
      - "**/angular.json"
  learnings:
    scope: "repository"

# Busca na web habilitada
web_search:
  enabled: true
