import {Component, Input, OnInit, ElementRef, ViewChild, ViewChildren, QueryList} from '@angular/core';
import {Router} from '@angular/router';
import {PlanoService} from '@base-core/plano/plano.service';
import {Plano} from '@base-core/plano/plano.model';
import {Config} from '@base-core/empresa/config.model';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {FormGroup, Validators} from '@angular/forms';
import {Cupom} from '@base-core/cupom-desconto/cupom.model';
import {PremioCupom} from '@base-core/cupom-desconto/premio-cupom.model';
import {ProdutoService} from '@base-core/produto/produto.service';
import Swal from 'sweetalert2';
import {connectableObservableDescriptor} from 'rxjs/internal/observable/ConnectableObservable';
import {TranslateService} from '@ngx-translate/core';

@Component({
  selector: 'pacto-plano-recorrente',
  templateUrl: './plano-recorrente.component.html',
  styleUrls: ['./plano-recorrente.component.scss']
})
export class PlanoRecorrenteComponent implements OnInit {
  vezesEscolhidasParcelarMatricula = 1;

  @Input() formGroup: FormGroup;
  @ViewChild('parcelasPlanoDiv') parcelasPlanoContainer: ElementRef<HTMLDivElement>;
  @ViewChild('parcelasPlanoDivP') parcelasPlanoContainerP: ElementRef<HTMLDivElement>;


  router: string;
  valorPrimeiraParcela: number;
  valorAdesao: number;
  valorProduto: number;
  valorAnuidadeDesconto: number;
  deatlhe: boolean[];

  parcelaIdex: number;

  constructor(private _router: Router,
              private empresaService: EmpresaService,
              private negociacaoService: NegociacaoService,
              private produtoService: ProdutoService,
              private planoService: PlanoService,
              private translateService: TranslateService
  ) {
    this.router = _router.url;
  }

  ngOnInit() {
    this.deatlhe = this.getParcelas().map(s => false);
    this.planoService.vezesEscolhidasParcelarTaxaMatricula = 1;
    this.getVezesEscolhidasParcelarMatricula();

  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  mostraPrimeiraParcelas(): void {

    const display = this.parcelasPlanoContainerP.nativeElement.style.display;
    if (display !== 'none') {
      this.parcelasPlanoContainerP.nativeElement.style.display = 'none';
    } else {
      this.parcelasPlanoContainerP.nativeElement.style.display = 'block';
    }
  }

  mostraDetalheaParcelas(index: number): void {
    this.parcelaIdex = this.negociacaoService.parcelas[index + 1].valor;
    this.deatlhe[index] = !this.deatlhe[index];
  }

  getDescricaoValorPrimeiraParcela() {

    this.valorPrimeiraParcela = (this.validarCobrarPrimeiraParcelaSempre() ? this.planoService.planoSelecionado.primeiraParcela : 0);
    this.valorAdesao = (this.getCupom() ? this.getValorAdesaoDescontoPrimeiraParcela() : this.getPlanoSelecionado().adesao);
    this.valorProduto = (this.cobrarProdutoJuntoAdesaoMatricula() ? this.getPlanoSelecionado().valorProdutos : 0);
    this.valorAnuidadeDesconto = (this.getPlanoSelecionado().mesAnuidade && this.getPlanoSelecionado().anuidadeAgora ?
        (this.getCupom() ? this.getValorAnuidadeDescontoPrimeiraParcela() :
          (this.getParcelarAnuidade() ? this.getValorAnuidadeParcelaUm() : this.getPlanoSelecionado().anuidade)) : 0) +
      this.getValorTotalProdutosSelecionados();

  }

  getValorAnuindade(): number {
    const valorAnuidadeExibir = this.planoService.planoSelecionado.anuidade;
    if (valorAnuidadeExibir !== undefined && valorAnuidadeExibir > 0 && this.getListaPremios() !== undefined &&
      this.getListaPremios().length > 0) {
      for (let i = 0; i < this.getListaPremios().length; i++) {
        const descricaoPremioArray = this.getListaPremios()[i].descricaoPremio;
        if (descricaoPremioArray === 'ANUIDADE PLANO RECORRENTE') {
          if (this.getListaPremios()[i].percentualDesconto !== 0.0) {
            return valorAnuidadeExibir - ((valorAnuidadeExibir * this.getListaPremios()[i].percentualDesconto) / 100);
          } else if (this.getListaPremios()[i].valorDesconto !== 0.0) {
            return valorAnuidadeExibir - this.getListaPremios()[i].valorDesconto;
          }
        }
      }
    }
    return valorAnuidadeExibir;
  }

  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }

  getMesAnoCobrancaAnuidade(): string {
    return (this.planoService.planoSelecionado ? this.planoService.planoSelecionado.mesAnuidade : '') +
      (this.negociacaoService.anoCobrancaAnuidade ? ' / ' + this.negociacaoService.anoCobrancaAnuidade : '');
  }

  getParcelarAnuidade(): boolean {
    return this.planoService.planoSelecionado != null &&
      this.planoService.planoSelecionado.parcelasAnuidade != null &&
      this.planoService.planoSelecionado.parcelasAnuidade.length > 0;
  }

  getMesAnuidade(): boolean {
    return this.planoService.planoSelecionado != null &&
      this.planoService.planoSelecionado.mesAnuidade === 'Parcela 1' ||
      (this.planoService.planoSelecionado.parcelasAnuidade != null &&
        this.planoService.planoSelecionado.parcelasAnuidade.length > 0 &&
        this.planoService.planoSelecionado.parcelasAnuidade[0].parcela === 1);
  }

  detalharParcelas(): boolean {
    return ((this.empresaService.config.detalharParcelaTelaCheckout != null &&
      this.empresaService.config.detalharParcelaTelaCheckout.valueOf() === 'true') ? true : false);
  }

  apresentarValorAnuidade(): boolean {
    return ((this.empresaService.config.apresentarvaloranuidade != null &&
      this.empresaService.config.apresentarvaloranuidade.valueOf() === 'true') ? true : false);
  }

  getProdutosPlano(): string[] {
    return this.getPlanoSelecionado().produtos.split('<br/>');
  }

  getMoeda(): String {
    return this.empresaService.unidadeSelecionada.moeda;
  }

  validarCobrarPrimeiraParcelaSempre(): boolean {
    const dia = new Date();
    const config = (this.getPlanoSelecionado().cobrarPrimeiraParcelaCompra != null &&
      this.getPlanoSelecionado().cobrarPrimeiraParcelaCompra) ? true : false;
    let diaVencimento = false;

    if (this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value != null) {
      if (dia.getDate() === this.formGroup.get('diavencimento').value) {
        diaVencimento = true;
      }
    }
    return (config || diaVencimento);
  }

  cobrarProdutoJuntoAdesaoMatricula(): boolean {
    const dia = new Date();
    let configProdJunto = ((this.empresaService.config.cobrarProdutoJuntoAdesaoMatricula != null &&
      this.empresaService.config.cobrarProdutoJuntoAdesaoMatricula.valueOf() === 'true'));
    let diaVencimento: boolean;

    if (this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value != null) {
      if (dia.getDate() !== this.formGroup.get('diavencimento').value && !configProdJunto) {
        diaVencimento = false;
      } else {
        configProdJunto = true;
        diaVencimento = true;
      }
    } else {
      configProdJunto = true;
      diaVencimento = true;
    }
    return (configProdJunto && diaVencimento);
  }

  // SUMIR OU APARECER DETALHES DAS PARCELAS DE ACORDO COM A REGRA DE NEGOCIO NA TELA DE CHECKOUT
  getClassAdeTextResponsive(): string {
    return this.validarCobrarPrimeiraParcelaSempre() ? 'ade' : 'ade-responsive';
  }

  id(texto, i): string {
    return texto + '-' + i;
  }

  getClassAdeValueResponsive(): string {
    return this.getPlanoSelecionado().adesao === 0.0 ? this.validarCobrarPrimeiraParcelaSempre() ?
        'parc-ade parc-ade-0' : 'parc-ade-responsive parc-ade-0' :
      this.validarCobrarPrimeiraParcelaSempre() ? 'parc-ade' : 'parc-ade-responsive';
  }

  getClassProdTextResponsive(): string {
    return this.validarCobrarPrimeiraParcelaSempre() ? 'prod' : 'prod-responsive';
  }

  getClassProdValueResponsive(): string {
    return this.getPlanoSelecionado().adesao === 0.0 ? this.validarCobrarPrimeiraParcelaSempre() ?
        'parc-prod parc-prod-0' : 'parc-prod-responsive parc-prod-0' :
      this.validarCobrarPrimeiraParcelaSempre() ? 'parc-prod' : 'parc-prod-responsive';
  }

  getClassAnuidTextResponsive(): string {
    if (this.getClassProdTextResponsive() === 'prod-responsive' && this.cobrarProdutoJuntoAdesaoMatricula()) {
      return 'anuid-responsive2';
    } else if (this.validarCobrarPrimeiraParcelaSempre() && (!(this.cobrarProdutoJuntoAdesaoMatricula()))) {
      return 'anuid-responsive2';
    } else {
      return this.cobrarProdutoJuntoAdesaoMatricula() ? 'anuid' : 'anuid-responsive';
    }
  }

  getClassAnuidValueResponsive(): string {
    if (this.getClassProdTextResponsive() === 'prod-responsive' && this.cobrarProdutoJuntoAdesaoMatricula()) {
      return 'parc-anuid-responsive2';
    } else if (this.validarCobrarPrimeiraParcelaSempre() && (!(this.cobrarProdutoJuntoAdesaoMatricula()))) {
      return 'parc-anuid-responsive2';
    } else {
      return this.getPlanoSelecionado().anuidade === 0.0 ? this.cobrarProdutoJuntoAdesaoMatricula() ?
          'parc-anuid parc-anuid-0' : 'parc-anuid-responsive parc-anuid-0' :
        this.cobrarProdutoJuntoAdesaoMatricula() ? 'parc-anuid' : 'parc-anuid-responsive';
    }
  }

  getClassBarraResponsive(): string {
    if (this.getClassAnuidTextResponsive() === 'anuid-responsive' && this.getClassAdeTextResponsive() === 'ade-responsive') {
      return 'linha-selec-responsive';
    } else if (this.getClassAnuidTextResponsive() === 'anuid-responsive2') {
      return 'linha-selec-responsive3';
    } else if (this.getClassAdeTextResponsive() === 'ade-responsive' && !this.cobrarProdutoJuntoAdesaoMatricula()) {
      return 'linha-selec-responsive2';
    } else {
      return 'linha-selec linha-selec-2';
    }
  }

  getCupom(): Cupom {
    return this.negociacaoService.cupom;
  }

  getListaPremios(): PremioCupom[] {
    const premioCupom = [];
    return this.negociacaoService.cupom == undefined ? premioCupom : this.negociacaoService.cupom.listaPremios;
  }

  limparCupom() {
    this.negociacaoService.cupom = null;
  }

  getPrimeiraParcelaValor(): number {
    return this.negociacaoService.valorPrimeiraParcela;
  }

  getValorProRata(): number {
    return this.negociacaoService.valorProRata;
  }

  getValorPrimeiraParcela(): number {
    let valorRetornar = this.validarCobrarPrimeiraParcelaSempre() ? this.planoService.planoSelecionado.primeiraParcela : 0;
    valorRetornar += this.getCupom() ? this.getValorAdesaoDescontoPrimeiraParcela() : this.getPlanoSelecionado().adesao;
    valorRetornar += this.cobrarProdutoJuntoAdesaoMatricula() ? this.getPlanoSelecionado().valorProdutos : 0;
    valorRetornar += (this.getPlanoSelecionado().mesAnuidade && this.getPlanoSelecionado().anuidadeAgora ?
      (this.getCupom() ? this.getValorAnuidadeDescontoPrimeiraParcela() : (this.getParcelarAnuidade() ?
        this.getValorAnuidadeParcelaUm() : this.getPlanoSelecionado().anuidade)) : 0);
    valorRetornar += this.getValorTotalProdutosSelecionados();
    valorRetornar += this.getPlanoSelecionado().matricula ? this.getPlanoSelecionado().matricula : 0;
    return valorRetornar;
  }

  getValorTotalProdutosSelecionados(): number {
    return this.produtoService.getValorTotalProdutosSelecionados();
  }

  getValorSeraCobradoHojeDesconto(): number {
    let parcela1 = this.getValorPrimeiraParcelaDesconto();
    parcela1 += this.valorAnuidadeParcela1();
    parcela1 += this.getValorAdesaoDesconto();
    parcela1 += this.getPlanoSelecionado().matricula ? this.getPlanoSelecionado().matricula : 0;

    return parcela1;
  }

  valorAnuidadeParcela1(): number {
    let anuidade = 0;
    if (this.planoService.planoSelecionado.anuidadeAgora) {
      anuidade = this.planoService.planoSelecionado.anuidade;
    }
    const planoEscolhidoAluno = this.planoService.planoSelecionado.nome;

    if (anuidade > 0) {
      let valorParcelaAnuidadeParcela1 = 0;
      const numeroParcelasAnuidade = this.planoService.planoSelecionado.parcelasAnuidade.length;
      for (let i = 0; i < this.planoService.planoSelecionado.parcelasAnuidade.length; i++) {
        if (this.planoService.planoSelecionado.parcelasAnuidade[i].parcela === 1) {
          valorParcelaAnuidadeParcela1 = this.planoService.planoSelecionado.parcelasAnuidade[i].valor;
        }
      }

      // If porque tem Anuidade Parcela e não parcelada
      // Existe dois for por ter dois cenários de Cupom, sendo o primeiro for para Desconto com Plano Especifico
      let temAnuidadeParcela1PlanoEspecifico = false;
      for (let i = 0; i < this.getListaPremios().length; i++) {
        const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
        const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
        if (descricaoPremio === 'ANUIDADE PLANO RECORRENTE' && planoEscolhidoAluno.toUpperCase() === planoPercorridoArray.toUpperCase()) {
          const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
          const valorDesconto = this.getListaPremios()[i].valorDesconto;
          if (percentualDesconto !== 0.0) {
            if (valorParcelaAnuidadeParcela1 > 0) {
              anuidade = valorParcelaAnuidadeParcela1 - ((valorParcelaAnuidadeParcela1 * percentualDesconto) / 100);
            } else {
              anuidade = anuidade - ((anuidade * percentualDesconto) / 100);
            }
            temAnuidadeParcela1PlanoEspecifico = true;
          } else if (valorDesconto !== 0.0) {
            if (valorParcelaAnuidadeParcela1 > 0) {
              anuidade = valorParcelaAnuidadeParcela1 - (valorDesconto / numeroParcelasAnuidade);
            } else {
              anuidade = anuidade - (valorDesconto / numeroParcelasAnuidade);
            }
            temAnuidadeParcela1PlanoEspecifico = true;
          }
        }
      }
      // Existe dois for por ter dois cenários de Cupom, sendo o segundo for para Desconto sem Plano Especifico
      if (!temAnuidadeParcela1PlanoEspecifico) {
        for (let i = 0; i < this.getListaPremios().length; i++) {
          const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
          const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
          if (descricaoPremio === 'ANUIDADE PLANO RECORRENTE' && planoPercorridoArray === '') {
            const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
            const valorDesconto = this.getListaPremios()[i].valorDesconto;
            if (percentualDesconto !== 0.0) {
              if (valorParcelaAnuidadeParcela1 > 0) {
                anuidade = valorParcelaAnuidadeParcela1 - ((valorParcelaAnuidadeParcela1 * percentualDesconto) / 100);
              } else {
                anuidade = anuidade - ((anuidade * percentualDesconto) / 100);
              }
              temAnuidadeParcela1PlanoEspecifico = true;
            } else if (valorDesconto !== 0.0) {
              if (valorParcelaAnuidadeParcela1 > 0) {
                anuidade = valorParcelaAnuidadeParcela1 - (valorDesconto / numeroParcelasAnuidade);
              } else {
                anuidade = anuidade - (valorDesconto / numeroParcelasAnuidade);
              }
              temAnuidadeParcela1PlanoEspecifico = true;
            }
          }
        }
      }
    }
    return anuidade;
  }

  getValorPrimeiraParcelaDesconto(): number {
    let parcela1 = this.validarCobrarPrimeiraParcelaSempre() ? this.planoService.planoSelecionado.primeiraParcela : 0;
    const planoEscolhidoAluno = this.planoService.planoSelecionado.nome;
    let temDescontoParcela1PlanoEspecifico = false;

    // ==== INICIO DESCONTO PARCELA 1 ====
    // Existe dois for por ter dois cenários de Cupom, sendo o primeiro for para Desconto com Plano Especifico
    for (let i = 0; i < this.getListaPremios().length; i++) {
      const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
      const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
      if (descricaoPremio === 'PARCELA 1' && planoEscolhidoAluno.toUpperCase() === planoPercorridoArray.toUpperCase()) {
        const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
        const valorDesconto = this.getListaPremios()[i].valorDesconto;
        if (percentualDesconto !== 0.0) {
          parcela1 = parcela1 - ((parcela1 * percentualDesconto) / 100);
          temDescontoParcela1PlanoEspecifico = true;
        } else if (valorDesconto !== 0.0) {
          parcela1 = parcela1 - valorDesconto;
          temDescontoParcela1PlanoEspecifico = true;
        } else {
          parcela1 = parcela1;
          temDescontoParcela1PlanoEspecifico = true;
        }
      }
    }
    // Existe dois for por ter dois cenários de Cupom, sendo o segundo for para Desconto sem Plano Especifico
    if (!temDescontoParcela1PlanoEspecifico) {
      for (let i = 0; i < this.getListaPremios().length; i++) {
        const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
        const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
        if (descricaoPremio === 'PARCELA 1' && planoPercorridoArray === '') {
          const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
          const valorDesconto = this.getListaPremios()[i].valorDesconto;
          if (percentualDesconto !== 0.0) {
            parcela1 = parcela1 - ((parcela1 * percentualDesconto) / 100);
          } else if (valorDesconto !== 0.0) {
            parcela1 = parcela1 - valorDesconto;
          } else {
            parcela1 = parcela1;
          }
        }
      }
    }
    // ==== FIM DESCONTO PARCELA 1 ====

    return parcela1;
  }

  getValorAdesaoDesconto(): number {
    let valorAdesaoRetornar = 0;
    if (this.planoService.planoSelecionado.adesao) {
      valorAdesaoRetornar = this.planoService.planoSelecionado.adesao;
    }
    const planoEscolhidoAluno = this.planoService.planoSelecionado.nome;

    if (valorAdesaoRetornar > 0) {
      // Existe dois for por ter dois cenários de Cupom, sendo o primeiro for para Desconto com Plano Especifico
      let temAdesaoPlanoEspecifico = false;
      for (let i = 0; i < this.getListaPremios().length; i++) {
        const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
        const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
        if (descricaoPremio === 'ADESÃO PLANO RECORRENTE' && planoEscolhidoAluno.toUpperCase() === planoPercorridoArray.toUpperCase()) {
          const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
          const valorDesconto = this.getListaPremios()[i].valorDesconto;
          if (percentualDesconto !== 0.0) {
            valorAdesaoRetornar = valorAdesaoRetornar - ((valorAdesaoRetornar * percentualDesconto) / 100);
            temAdesaoPlanoEspecifico = true;
          } else if (valorDesconto !== 0.0) {
            valorAdesaoRetornar = valorAdesaoRetornar - valorDesconto;
            temAdesaoPlanoEspecifico = true;
          }
        }
      }
      // Existe dois for por ter dois cenários de Cupom, sendo o segundo for para Desconto sem Plano Especifico
      if (!temAdesaoPlanoEspecifico) {
        for (let i = 0; i < this.getListaPremios().length; i++) {
          const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
          const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
          if (descricaoPremio === 'ADESÃO PLANO RECORRENTE' && planoPercorridoArray === '') {
            const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
            const valorDesconto = this.getListaPremios()[i].valorDesconto;
            if (percentualDesconto !== 0.0) {
              valorAdesaoRetornar = valorAdesaoRetornar - ((valorAdesaoRetornar * percentualDesconto) / 100);
              temAdesaoPlanoEspecifico = true;
            } else if (valorDesconto !== 0.0) {
              valorAdesaoRetornar = valorAdesaoRetornar - valorDesconto;
              temAdesaoPlanoEspecifico = true;
            }
          }
        }
      }
    }
    return valorAdesaoRetornar;
  }

  getValorProduto(): number {
    return this.planoService.planoSelecionado.valorProdutos;
  }

  getValorAdesaoDescontoPrimeiraParcela(): number {
    let temPremio = false;
    const adesao = this.getPlanoSelecionado().adesao;
    for (let i = 0; i < this.getListaPremios().length; i++) {
      if (this.getListaPremios()[i].descricaoPremio === 'ADESÃO PLANO RECORRENTE') {
        temPremio = true;
        if (this.getListaPremios()[i].percentualDesconto !== 0.0) {
          return adesao - ((adesao * this.getListaPremios()[i].percentualDesconto) / 100);
        } else if (this.getListaPremios()[i].valorDesconto !== 0.0) {
          return adesao - this.getListaPremios()[i].valorDesconto;
        } else {
          return adesao;
        }
      }
    }
    return temPremio ? adesao : 0;
  }

  getValorAnuidadeDesconto(): number {
    let valorAnuidadeRetornar = 0;
    if (this.planoService.planoSelecionado.anuidade) {
      valorAnuidadeRetornar = this.planoService.planoSelecionado.anuidade;
    }
    const planoEscolhidoAluno = this.planoService.planoSelecionado.nome;

    if (valorAnuidadeRetornar > 0) {
      // Existe dois for por ter dois cenários de Cupom, sendo o primeiro for para Desconto com Plano Especifico
      let temAnuidadePlanoEspecifico = false;
      for (let i = 0; i < this.getListaPremios().length; i++) {
        const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
        const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
        if (descricaoPremio === 'ANUIDADE PLANO RECORRENTE' && planoEscolhidoAluno.toUpperCase() === planoPercorridoArray.toUpperCase()) {
          const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
          const valorDesconto = this.getListaPremios()[i].valorDesconto;
          if (percentualDesconto !== 0.0) {
            valorAnuidadeRetornar = valorAnuidadeRetornar - ((valorAnuidadeRetornar * percentualDesconto) / 100);
            temAnuidadePlanoEspecifico = true;
          } else if (valorDesconto !== 0.0) {
            valorAnuidadeRetornar = valorAnuidadeRetornar - valorDesconto;
            temAnuidadePlanoEspecifico = true;
          }
        }
      }
      // Existe dois for por ter dois cenários de Cupom, sendo o segundo for para Desconto sem Plano Especifico
      if (!temAnuidadePlanoEspecifico) {
        for (let i = 0; i < this.getListaPremios().length; i++) {
          const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
          const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
          if (descricaoPremio === 'ANUIDADE PLANO RECORRENTE' && planoPercorridoArray === '') {
            const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
            const valorDesconto = this.getListaPremios()[i].valorDesconto;
            if (percentualDesconto !== 0.0) {
              valorAnuidadeRetornar = valorAnuidadeRetornar - ((valorAnuidadeRetornar * percentualDesconto) / 100);
              temAnuidadePlanoEspecifico = true;
            } else if (valorDesconto !== 0.0) {
              valorAnuidadeRetornar = valorAnuidadeRetornar - valorDesconto;
              temAnuidadePlanoEspecifico = true;
            }
          }
        }
      }
    }
    return valorAnuidadeRetornar;
  }

  getValorAnuidadeDescontoPrimeiraParcela(): number {
    let temPremio = false;
    const anuidade = this.getPlanoSelecionado().anuidade;
    for (let i = 0; i < this.getListaPremios().length; i++) {
      if (this.getListaPremios()[i].descricaoPremio === 'ANUIDADE PLANO RECORRENTE') {
        temPremio = true;
        if (this.getListaPremios()[i].percentualDesconto !== 0.0) {
          return ((anuidade / 100) * this.getListaPremios()[i].percentualDesconto);
        } else if (this.getListaPremios()[i].valorDesconto !== 0.0) {
          return anuidade - this.getListaPremios()[i].valorDesconto;
        } else {
          return anuidade;
        }
      }
    }
    return temPremio ? anuidade : 0;
  }

  getValorAnuidadeParcelaUm(): number {
    for (let i = 0; i < this.getPlanoSelecionado().parcelasAnuidade.length; i++) {
      if (this.getPlanoSelecionado().parcelasAnuidade[i].numero === 1) {
        return this.getPlanoSelecionado().parcelasAnuidade[i].valor;
      }
    }
    return 0;
  }

  getParcelaDescricaoPluSing(valor): string {
    if (valor > 1) {
      return this.translateService.instant('checkout.a-anuidade-deste-plano-e-dividida-em-x-parcelas').replace('{nrParcela}', valor);
    }
    return this.translateService.instant('checkout.a-anuidade-deste-plano-contem-uma-unica-parcela');
  }

  getAnuidadeDescricao(obj, valor): string {
    if (valor > 1) {
      let anuidadeDescricao = this.translateService.instant('checkout.a-parcela-x-sera-cobrada')
        .replace('{numeroParcela}', obj.numero);
      anuidadeDescricao += ' ' + obj.parcelaApresentar.toLowerCase();
      return anuidadeDescricao;
    }
    return `${this.translateService.instant('checkout.parcela-um-que-sera-cobrada')} ${obj.parcelaApresentar.toLowerCase()}.`;
  }

  mostraParcelas(): void {
    const display = this.parcelasPlanoContainer.nativeElement.style.display;
    if (display !== 'none') {
      this.parcelasPlanoContainer.nativeElement.style.display = 'none';
    } else {
      this.parcelasPlanoContainer.nativeElement.style.display = 'block';
    }
  }

  getParcelas(): string[] {
    const valores: Array<string> = [];
    let numeroParcela = 0;
    let anoEMes;
    let descricaoParcela: Array<string> = [];
    let valorAnuidade = 0;
    for (let i = 0; i < this.negociacaoService.parcelas.length; i++) {
      numeroParcela++;
      anoEMes = this.negociacaoService.parcelas[i].descricao.split('-');
      descricaoParcela = anoEMes[anoEMes.length - 1];
      for (let x = 0; x <= this.planoService.planoSelecionado.parcelasAnuidade.length - 1; x++) {
        if (this.planoService.planoSelecionado.parcelasAnuidade[x].parcela === numeroParcela) {
          valorAnuidade = this.planoService.planoSelecionado.parcelasAnuidade[x].valor;

          // Utilizado Descrição pela pressa e porquê as opções retornadas pelo backend são limitadas
          // Se o cliente mudar a descrição do produto padrão no ZW, esse cálculo do Desconto não vai funcionar
          // O ideal leva mais tempo para implementar, pois precisa ajustar o backend para retornar o Tipo de Produto = Taxa de Anuidade
          // Plano Recorrência (TA)
          let valorDescontoCupom = 0;
          if (valorAnuidade > 0 && this.getListaPremios().length > 0) {
            for (let f = 0; f < this.getListaPremios().length; f++) {
              if (this.getListaPremios()[f].descricaoPremio === 'ANUIDADE PLANO RECORRENTE' &&
                this.getListaPremios()[f].percentualDesconto > 0) {
                valorDescontoCupom = valorAnuidade * (this.getListaPremios()[f].percentualDesconto / 100);
              } else if (this.getListaPremios()[f].descricaoPremio === 'ANUIDADE PLANO RECORRENTE' &&
                this.getListaPremios()[f].valorDesconto > 0) {
                const numeroParcelasAnuidade = this.planoService.planoSelecionado.parcelasAnuidade.length;
                valorDescontoCupom = this.getListaPremios()[f].valorDesconto / numeroParcelasAnuidade;
              }
            }
          }

          valorAnuidade = valorAnuidade - valorDescontoCupom;
          x = this.planoService.planoSelecionado.parcelasAnuidade.length + 1;
        } else {
          valorAnuidade = 0;
        }
      }
      valores.push(((valorAnuidade > 0 || (this.getPlanoSelecionado().matricula > 0 && this.getVezesEscolhidasParcelarMatricula() > i))
          ? 'true  |' : 'false |') + numeroParcela + `ª ${this.translateService.instant('planos.plano-recorrente.parcela')} -` +
        descricaoParcela + ' ' + this.getMoeda() + ' '
        + ((this.planoService.vezesEscolhidasParcelarTaxaMatricula > i ?
            this.getPlanoSelecionado().matricula / this.planoService.vezesEscolhidasParcelarTaxaMatricula : 0) +
          (this.getValorParcelaDesconto(this.negociacaoService.parcelas[i].valor + valorAnuidade, numeroParcela))).toFixed(2));
    }
    return valores;
  }

  getVezesEscolhidasParcelarMatricula(): number {
    if (this.vezesEscolhidasParcelarMatricula > 0 && this.vezesEscolhidasParcelarMatricula <=
      this.getPlanoSelecionado().nrVezesParcelarMatricula) {
      this.planoService.vezesEscolhidasParcelarTaxaMatricula = this.vezesEscolhidasParcelarMatricula;
      return this.vezesEscolhidasParcelarMatricula;
    } else {

      return 1;
    }
  }

  counter(i: number) {
    return new Array(i);
  }


  getDescricaoCobrancaPrimeiraParcela(): string {
    if (this.negociacaoService.descricaoCobrancaPrimeiraParcela) {
      return this.negociacaoService.descricaoCobrancaPrimeiraParcela.toLowerCase();
    }
    return this.translateService.instant('planos.plano-recorrente.hoje').toLowerCase();
  }


  getValorParcelaDesconto(valorParcela: number, parcelaSelecionada): number {
    let valorParcelas: number;
    if (this.getListaPremios().length > 0) {
      for (let i = 0; i < this.getListaPremios().length; i++) {
        if (
          (this.getListaPremios()[i].descricaoPremio === 'PARCELA ' + parcelaSelecionada)
          && (this.getListaPremios()[i].descricaoPlano.toUpperCase() === this.planoService.planoSelecionado.nome.toUpperCase())
        ) {
          if (this.getListaPremios()[i].percentualDesconto !== 0.0) {
            return valorParcela - ((valorParcela * this.getListaPremios()[i].percentualDesconto) / 100);
          } else if (this.getListaPremios()[i].valorDesconto !== 0.0) {
            return valorParcela - this.getListaPremios()[i].valorDesconto;
          }
        }
      }
      for (let i = 0; i < this.getListaPremios().length; i++) {
        if ((this.getListaPremios()[i].descricaoPremio === 'PARCELA ' + parcelaSelecionada) &&
          this.getListaPremios()[i].descricaoPlano === '') {
          if (this.getListaPremios()[i].percentualDesconto !== 0.0) {
            return valorParcela - ((valorParcela * this.getListaPremios()[i].percentualDesconto) / 100);
          } else if (this.getListaPremios()[i].valorDesconto !== 0.0) {
            return valorParcela - this.getListaPremios()[i].valorDesconto;
          }
        }
      }
    } else {
      valorParcelas = valorParcelas;
    }
    return valorParcela;
  }

  getValidarData(): void {
    if (this.formGroup.get('dataInicioContrato') != null && this.formGroup.get('dataInicioContrato').value != null) {
      const partesData = this.formGroup.get('dataInicioContrato').value.split('-');
      const data = new Date(partesData[0], partesData[1] - 1, partesData[2], 23, 59, 59);
      if (data < new Date()) {
        Swal.fire({
          type: 'error',
          title: 'Inicio do Contrato!',
          text: 'A data informada não pode ser menor que hoje.',
          showConfirmButton: true
        });
        this.formGroup.get('dataInicioContrato').setValue(null);
        return;
      }
    }
  }

  exibirPremio(premio: PremioCupom, planoSelecionado: string): boolean {
    let existePremioPlanoEspecifico = false;
    let existePremioPlanoGeral = false;
    // Verifica se para esse premio, tem mais de uma configurado para Plano Especifico e Geral
    for (let i = 0; i < this.getListaPremios().length; i++) {
      const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
      const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
      if (descricaoPremio.toUpperCase() === premio.descricaoPremio.toUpperCase() &&
        planoPercorridoArray.toUpperCase() === planoSelecionado.toUpperCase()) {
        existePremioPlanoEspecifico = true;
      } else if (descricaoPremio.toUpperCase() === premio.descricaoPremio.toUpperCase() && planoPercorridoArray === '') {
        existePremioPlanoGeral = true;
      }
    }
    // Se premio que está validando, só tiver para especifico, retorna true para usar ele
    // Se premio que está validando, só tiver para geral, retorna true para usar ele
    // Se premio que está validando, tiver para especifico e geral, valida se o premio atual é o especifico, se for usa ele e se
    // não for não usa
    if (existePremioPlanoEspecifico && !existePremioPlanoGeral &&
      premio.descricaoPlano.toUpperCase() === planoSelecionado.toUpperCase()) {
      return true;
    } else if (!existePremioPlanoEspecifico && existePremioPlanoGeral) {
      return true;
    } else if (existePremioPlanoEspecifico && existePremioPlanoGeral &&
      planoSelecionado.toUpperCase() === premio.descricaoPlano.toUpperCase()) {
      return true;
    }
    return false;
  }

}
